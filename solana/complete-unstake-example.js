import { completeUnstake, loadKeypair } from './batch-transfer.js';

// Example: How to call the complete_unstake function from STAKING_IDL
async function main() {
  try {
    // Load your wallet keypair (replace with your actual wallet path or private key)
    const walletKeypair = loadKeypair("path/to/your/wallet.json"); // or use base58 private key string
    
    // Configure the complete unstake parameters
    const completeUnstakeArgs = {
      stakingPool: "YOUR_STAKING_POOL_ADDRESS",     // Replace with actual staking pool address
      stakingVault: "YOUR_STAKING_VAULT_ADDRESS",   // Replace with staking vault address
      rewardVault: "YOUR_REWARD_VAULT_ADDRESS",     // Replace with reward vault address
      stakingMint: "YOUR_STAKING_TOKEN_MINT",       // Replace with staking token mint address
      rewardMint: "YOUR_REWARD_TOKEN_MINT",         // Replace with reward token mint address
    };

    console.log("Starting complete unstake process...");
    console.log("Wallet:", walletKeypair.publicKey.toBase58());
    console.log("Staking Pool:", completeUnstakeArgs.stakingPool);
    console.log("Staking Vault:", completeUnstakeArgs.stakingVault);
    console.log("Reward Vault:", completeUnstakeArgs.rewardVault);
    console.log("Staking Mint:", completeUnstakeArgs.stakingMint);
    console.log("Reward Mint:", completeUnstakeArgs.rewardMint);

    // Call the complete unstake function
    const txSignature = await completeUnstake(walletKeypair, completeUnstakeArgs);
    
    console.log("🎉 Complete unstake successful!");
    console.log("Transaction signature:", txSignature);
    console.log(`View on Solscan: https://solscan.io/tx/${txSignature}`);
    
  } catch (error) {
    console.error("❌ Complete unstake failed:");
    console.error(error.message);
    
    // Log more details if available
    if (error.logs) {
      console.error("Program logs:", error.logs);
    }
    
    // Common error scenarios
    if (error.message.includes("Account not found")) {
      console.error("💡 Tip: Make sure you have tokens staked and the addresses are correct");
    } else if (error.message.includes("unlock")) {
      console.error("💡 Tip: You might need to call 'request_unlock' first and wait for the unlock period");
    } else if (error.message.includes("insufficient")) {
      console.error("💡 Tip: Make sure you have enough SOL for transaction fees");
    }
  }
}

// Run the example
main().catch(console.error);
