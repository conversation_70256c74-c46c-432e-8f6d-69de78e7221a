# Complete Unstake Function Usage Guide

This guide explains how to use the `completeUnstake` function to complete the unstaking process from the STAKING_IDL program.

## Overview

The `completeUnstake` function allows you to finalize the unstaking process for tokens that have been previously staked. This function typically needs to be called after a `request_unlock` has been made and the unlock period has passed.

## Function Signature

```javascript
async function completeUnstake(walletKeypair, args)
```

### Parameters

- `walletKeypair`: A Solana Keypair object representing the wallet that owns the staked tokens
- `args`: An object containing the following properties:
  - `stakingPool`: The staking pool address (PublicKey string)
  - `stakingVault`: The staking vault address (PublicKey string)
  - `rewardVault`: The reward vault address (PublicKey string)
  - `stakingMint`: The staking token mint address (PublicKey string)
  - `rewardMint`: The reward token mint address (PublicKey string)

## Required Information

Before calling the complete unstake function, you need:

1. **Staking Pool Address**: The address of the staking pool where your tokens are staked
2. **Staking Vault Address**: The vault that holds the staked tokens
3. **Reward Vault Address**: The vault that holds reward tokens
4. **Staking Mint Address**: The mint address of the token you staked
5. **Reward Mint Address**: The mint address of the reward token
6. **Wallet Keypair**: Your wallet's private key (as file path or base58 string)

## Usage Example

```javascript
import { completeUnstake, loadKeypair } from './batch-transfer.js';

async function unstakeExample() {
  // Load your wallet
  const walletKeypair = loadKeypair("path/to/wallet.json"); // or base58 private key
  
  // Configure complete unstake parameters
  const completeUnstakeArgs = {
    stakingPool: "YourStakingPoolAddressHere",
    stakingVault: "YourStakingVaultAddressHere",
    rewardVault: "YourRewardVaultAddressHere",
    stakingMint: "YourStakingTokenMintHere",
    rewardMint: "YourRewardTokenMintHere",
  };

  try {
    const txSignature = await completeUnstake(walletKeypair, completeUnstakeArgs);
    console.log("Success! Transaction:", txSignature);
  } catch (error) {
    console.error("Failed:", error.message);
  }
}
```

## What the Function Does

1. **Creates Anchor Provider**: Sets up connection to Solana network
2. **Derives User Stake PDA**: Automatically calculates the user stake account address using:
   - Seeds: `["user_stake", stakingPool, user]`
3. **Gets Token Accounts**: Finds associated token accounts for:
   - User's staking token account
   - User's reward token account
4. **Calls Complete Unstake**: Executes the complete_unstake instruction with all required accounts
5. **Returns Transaction**: Returns the transaction signature on success

## Account Derivations

The function automatically handles these account derivations:

- **User Stake PDA**: `["user_stake", stakingPool, user]`
- **User Token Account**: Associated token account for staking mint
- **User Reward Account**: Associated token account for reward mint

## Prerequisites

Before calling `completeUnstake`, you typically need to:

1. **Have Staked Tokens**: You must have previously staked tokens in the pool
2. **Request Unlock**: Call `request_unlock` function first
3. **Wait for Unlock Period**: Wait for the required unlock duration to pass
4. **Have SOL for Fees**: Ensure you have enough SOL for transaction fees

## Error Handling

Common errors and solutions:

- **"Account not found"**: 
  - Check if you have tokens staked
  - Verify all addresses are correct
  - Ensure you've called `request_unlock` first

- **"Unlock period not met"**: 
  - Wait for the full unlock duration to pass
  - Check the staking pool's unlock requirements

- **"Insufficient funds"**: 
  - Add more SOL to your wallet for transaction fees

- **"Invalid owner"**: 
  - Make sure you're using the correct wallet that staked the tokens

## Running the Example

1. Update `complete-unstake-example.js` with your actual values
2. Run: `node solana/complete-unstake-example.js`

## Program Information

- **Program ID**: `EsybJRDoqxAnnBBwjvQ42X1xkgokZEKmSP9Y8sZV7bzN`
- **Network**: Mainnet Beta
- **Function**: `complete_unstake` (no arguments required)

## Notes

- The function connects to mainnet-beta by default
- Transaction fees are paid by the wallet
- Both staked tokens and accumulated rewards are returned
- The user stake account is typically closed after successful unstaking
- Make sure to have the correct unlock timing before calling this function
